const mongoose = require('mongoose');
const _ = require('lodash')
const mongoConnections = require('../connections/mongo')
const UserModel = require('./user');

const MeetingSchedule = new mongoose.Schema({
  startTime: {
    type: Number,
    required: true
  },
  endTime: {
    type: Number
  },
  officers: [{
    type: mongoose.Schema.Types.ObjectId,
    required: true,
    ref: 'User'
  }],
  status:{
    type: Number,
    default: 1
  },
  topic: {
    type: String,
    required: true
  },
  content: {
    type: String
  },
  attachments: [{
    type: String, 
  }],
  assignedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  createdAt: {
    type: Number,
    default: Date.now
  },
  updatedAt: {
    type: Number,
    default: Date.now
  }
}, { id: false, versionKey: false });

module.exports = mongoConnections('master').model('MeetingSchedule', MeetingSchedule);
