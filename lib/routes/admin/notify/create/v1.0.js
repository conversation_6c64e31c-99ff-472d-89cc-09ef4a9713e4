const _ = require('lodash')
const async = require('async')
const config = require('config')
const mongoose = require('mongoose')
const CONSTANTS = require('../../../../const')
const SavedNotification = require('../../../../models/savedNotification')
const PushNotifyManager = require('../../../../jobs/pushNotify')
const MESSAGES = require('../../../../message');
const Unit = require('../../../../models/unit');


module.exports = (req, res) => {
  let {
    title = '',
    titlePush = '',
    messagePush = '',
    content = '',
    type = 'all', // 'all', 'unit', 'user'
    units = [],
    users = [],
    image = '',
  } = req.body;

  // Trim các string inputs
  title = title.trim();
  titlePush = titlePush.trim();
  messagePush = messagePush.trim();

  let createdNotification = {};

  const checkParams = (next) => {
    // Kiểm tra tiêu đề
    if (!title) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SAVED_NOTIFICATION.MISSING_TITLE
      });
    }

    if(!content) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SAVED_NOTIFICATION.MISSING_CONTENT
      });
    }
    // Kiểm tra loại thông báo
    if (!['all', 'unit', 'user'].includes(type)) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SAVED_NOTIFICATION.INVALID_TYPE
      });
    }

    // Kiểm tra người nhận dựa trên type
    if (type === 'unit' && (!units || !units.length)) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SAVED_NOTIFICATION.MISSING_RECIPIENTS
      });
    }

    if (type === 'user' && (!users || !users.length)) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SAVED_NOTIFICATION.MISSING_RECIPIENTS
      });
    }

    next();
  };

  const validateUnits = (next) => {
    // Nếu type không phải 'unit' thì bỏ qua validation
    if (type !== 'unit') {
      return next();
    }

    // Kiểm tra định dạng ObjectId của các unit
    const invalidIds = units.filter(unitId => !mongoose.Types.ObjectId.isValid(unitId));
    if (invalidIds.length > 0) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: `Invalid unit ID format: ${invalidIds.join(', ')}`
        }
      });
    }

    // Kiểm tra xem các unit có tồn tại trong database không
    Unit.find({
      _id: { $in: units },
      status: 1 // Chỉ lấy các unit đang hoạt động
    }, (err, foundUnits) => {
      if (err) {
        return next({
          code: CONSTANTS.CODE.SYSTEM_ERROR,
          message: {
            head: 'Thông báo',
            body: 'Tổ công tác không hợp lệ'
          }
        });
      }

      // Kiểm tra xem tất cả units có tồn tại không
      if (foundUnits.length !== units.length) {
        const foundUnitIds = foundUnits.map(unit => unit._id.toString());
        const notFoundUnits = units.filter(unitId => !foundUnitIds.includes(unitId));
        
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Thông báo',
            body: `Không tìm thấy tổ công tác: ${notFoundUnits.join(', ')}`
          }
        });
      }

      next();
    });
  };

  const createNotification = (next) => {
    data = {
      link: 'ListNotification',
      extras: {
        content
      }
    }
    let objCreate = {
      image,
      title,
      type,
      data,
      status: 1,
      createdAt: Date.now(),
      updatedAt: Date.now()
    };


    if (type === 'unit' && units.length) {
      objCreate.units = units;
    }

    if (type === 'user' && users.length) {
      objCreate.users = users;
    }

    SavedNotification.create(objCreate, (err, result) => {
      if (err) {
        return next(err);
      }

      createdNotification = result;
      next();
    });
  };


  const returnResult = (next) => {
    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      data: createdNotification
    });
  };

  async.waterfall([
    checkParams,
    validateUnits,
    createNotification,
    returnResult
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR
    });

    res.json(data || err);
  })
}