const _ = require('lodash');
const async = require('async');
const Joi = require('joi');

const statisticsService = require('../../../services/statisticsService');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');
const config = require('config');

const { Configuration, OpenAIApi } = require("openai");

const configuration = new Configuration({
  apiKey: config.ai.apiKey,
  basePath: config.ai.url,
  baseOptions: {
    headers: {
      'Authorization': `Bearer ${config.ai.apiKey}`,
    },
  },
});

const client = new OpenAIApi(configuration);


module.exports = (req, res) => {
  let {
    prompt = '',
    type = 'general', // 'general', 'attendance', 'performance'
    timeRange = '7days',
    unitIds = []
  } = req.body;


  let statisticsData = {
    "Báo cáo theo tổ công tác":{
      "Tổ An Ninh":{
        "Đang làm việc":10,
        "Đang trực/công tác":5,
        "Nghỉ":2
      },
      "Tổ Tổng hợp":{
        "Đang làm việc":12,
        "Đang trực/công tác":3,
        "Nghỉ":0
      },
      "Tổ Cảnh sát Phòng chống tội phạm":{
        "Đang làm việc":15,
        "Đang trực/công tác":3,
        "Nghỉ":1
      },
      "Tổ Cảnh sát trật tự":{
        "Đang làm việc":10,
        "Đang trực/công tác":5,
        "Nghỉ":3
      },
      "Tổ Cảnh sát khu vực":{
        "Đang làm việc":14,
        "Đang trực/công tác":5,
        "Nghỉ":0
      }
    },
    "Thống kê tình trạng cán bộ đi làm":{
      "Tổng số cán bộ": 61,
      "Điểm danh đúng giờ": 42,
      "Không điểm danh": 12,
      "Điểm danh muộn": 2,
      "Nghỉ": 5
    }
  };
  let aiResponse = {};

  const validateParams = (next) => {
    const schema = Joi.object({
      prompt: Joi.string().required().min(10).max(1000),
      type: Joi.string().valid('general', 'attendance', 'performance').default('general'),
      timeRange: Joi.string().valid('1day', '7days', '30days', '90days').default('7days'),
      unitIds: Joi.array().items(Joi.string()).default([])
    });

    const { error, value } = schema.validate({ prompt, type, timeRange, unitIds });
    
    if (error) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: error.details[0].message
      });
    }

    // Update validated values
    prompt = value.prompt;
    type = value.type;
    timeRange = value.timeRange;
    unitIds = value.unitIds;

    next();
  };

  const getStatisticsData = (next) => {
    // Sử dụng dữ liệu mẫu đã có sẵn
    next();
  };

  const callAI = (next) => {
    // Tạo context từ dữ liệu thống kê
    const contextPrompt = `
Dữ liệu thống kê hiện tại:
${JSON.stringify(statisticsData, null, 2)}

Yêu cầu phân tích thống kê dữ liệu cán bộ làm việc của các tổ công tác trong thời gian ngày 21/08.

Hãy phân tích dữ liệu và đưa ra báo cáo chi tiết, bao gồm:
1. Phân tích quân số và phân bổ lực lượng
2. Theo dõi kỷ luật và chấp hành giờ giấc
3. So sánh giữa các tổ công tác
4. Đưa ra nhận định về tình hình hoạt động
5. Gợi ý hành động

Nguyên tắc:
- Tuyệt đối bám số liệu input. Không suy diễn nếu thiếu bằng chứng.
- Giọng văn hành chính, ngắn gọn.
- Không sử dụng từ ngữ cảm tính, không bình luận chủ quan.
- Không sử dụng từ ngữ không phù hợp với văn hóa công an nhân dân.
- Không sử dụng từ ngữ không phù hợp với văn hóa Việt Nam.`;

    client.createChatCompletion({
      model: config.ai.model,
      messages: [
        {
          role: "system",
          content: "Bạn là một chuyên gia phân tích dữ liệu và báo cáo cho cơ quan công an. Hãy phân tích dữ liệu một cách chính xác, chi tiết và đưa ra những nhận định hữu ích."
        },
        {
          role: "user",
          content: contextPrompt
        }
      ],
      max_tokens: 60000,
      temperature: 0.2
    })
    .then(response => {
      // Kiểm tra an toàn response structure
      if (!response.data || !response.data.choices || response.data.choices.length === 0) {
        console.error('Invalid response structure:', response.data);
        return next({
          code: CONSTANTS.CODE.SYSTEM_ERROR,
          message: 'Invalid AI response structure'
        });
      }

      aiResponse = {
        content: response.data.choices[0].message.content,
        usage: response.data.usage,
        model: response.data.model
      };
      next();
    })
    .catch(error => {
      console.error('OpenAI API Error:', error);
      next({
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR
      });
    });
  };

  const returnResult = (next) => {  
    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      data: {
        report: aiResponse.content,
        metadata: {
          type,
          timeRange,
          unitIds,
          prompt,
          statisticsData,
          aiModel: aiResponse.model,
          usage: aiResponse.usage,
          generatedAt: Date.now()
        }
      }
    });
  };

  async.waterfall([
    // validateParams,
    getStatisticsData,
    callAI,
    returnResult
  ], (err, data) => {
    if (_.isError(err)) {
      global.logger && global.logger.logError([err], req.originalUrl, req.body);
      global.MailUtil && global.MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  });
};
