const _ = require('lodash');
const async = require('async');
const Joi = require('joi');

const statisticsService = require('../../../services/statisticsService');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');
const config = require('config');

const { Configuration, OpenAIApi } = require("openai");

const configuration = new Configuration({
  apiKey: config.ai.apiKey,
  basePath: config.ai.url,
  baseOptions: {
    headers: {
      'Authorization': `Bearer ${config.ai.apiKey}`,
    },
  },
});

const client = new OpenAIApi(configuration);


module.exports = (req, res) => {
  let {
    prompt = '',
    type = 'general', // 'general', 'attendance', 'performance'
    timeRange = '7days',
    unitIds = []
  } = req.body;

  let statisticsData = {
    "Báo cáo theo lĩnh vực":{
      "20/08":{
        "An ninh trật tự":10,
        "Mâu thuẫn":5,
        "Tai nạn giao thông":2,
        "Số vụ cháy":1,
        "Vi phạm giao thông":8
      },
      "21/08":{
        "An ninh trật tự":19,
        "Mâu thuẫn":0,
        "Tai nạn giao thông":3,
        "Số vụ cháy":0,
        "Vi phạm giao thông":20
      }
    },
    "Báo cáo theo khu vực":{
      "20/08":{
        "Hùng Vương": 5,
        "Sở Dầu": 3,
        "Thượng Lý": 2,
        "Hoàng Văn Thụ": 1,
        "Minh Khai": 4,
        "Phan Bôi Châu": 2
      },

      "21/08":{
        "Hùng Vương": 10,
        "Sở Dầu": 3,
        "Thượng Lý": 5,
        "Hoàng Văn Thụ": 6,
        "Minh Khai": 2,
        "Phan Bôi Châu": 6
      }
    }
    
  };
  let aiResponse = {};

  const validateParams = (next) => {
    const schema = Joi.object({
      prompt: Joi.string().required().min(10).max(1000),
      type: Joi.string().valid('general', 'attendance', 'performance').default('general'),
      timeRange: Joi.string().valid('1day', '7days', '30days', '90days').default('7days'),
      unitIds: Joi.array().items(Joi.string()).default([])
    });

    const { error, value } = schema.validate({ prompt, type, timeRange, unitIds });
    
    if (error) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: error.details[0].message
      });
    }

    // Update validated values
    prompt = value.prompt;
    type = value.type;
    timeRange = value.timeRange;
    unitIds = value.unitIds;

    next();
  };

  const getStatisticsData = (next) => {
    // Sử dụng dữ liệu mẫu đã có sẵn
    next();
  };

  const callAI = (next) => {
    // Tạo context từ dữ liệu thống kê
    const contextPrompt = `
Dữ liệu thống kê hiện tại:
${JSON.stringify(statisticsData, null, 2)}

Yêu cầu phân tích so sánh dữ liệu về các đầu mục vụ việc xảy ra trong khoảng thời gian ngày 20 và 21 tháng 8

Hãy phân tích dữ liệu và đưa ra báo cáo chi tiết, bao gồm:
1. Phân tích thống kê – xu hướng
2. Đánh giá rủi ro – cảnh báo sớm
3. Gợi ý hành động cho cán bộ
4. Đề xuất và khuyến nghị cải thiện

Nguyên tắc:
- Tuyệt đối bám số liệu input. Không suy diễn nếu thiếu bằng chứng.
- Giọng văn hành chính, ngắn gọn.
- Không sử dụng từ ngữ cảm tính, không bình luận chủ quan.
- Không sử dụng từ ngữ không phù hợp với văn hóa công an nhân dân.
- Không sử dụng từ ngữ không phù hợp với văn hóa Việt Nam.`;

    client.createChatCompletion({
      model: config.ai.model,
      messages: [
        {
          role: "system",
          content: "Bạn là một chuyên gia phân tích dữ liệu và báo cáo cho cơ quan công an. Hãy phân tích dữ liệu một cách chính xác, chi tiết và đưa ra những nhận định hữu ích."
        },
        {
          role: "user",
          content: contextPrompt
        }
      ],
      max_tokens: 60000,
      temperature: 0.2
    })
    .then(response => {
      // Kiểm tra an toàn response structure
      if (!response.data || !response.data.choices || response.data.choices.length === 0) {
        console.error('Invalid response structure:', response.data);
        return next({
          code: CONSTANTS.CODE.SYSTEM_ERROR,
          message: 'Invalid AI response structure'
        });
      }

      aiResponse = {
        content: response.data.choices[0].message.content,
        usage: response.data.usage,
        model: response.data.model
      };
      next();
    })
    .catch(error => {
      console.error('OpenAI API Error:', error);
      next({
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR
      });
    });
  };

  const returnResult = (next) => {  
    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      data: {
        report: aiResponse.content,
        metadata: {
          type,
          timeRange,
          unitIds,
          prompt,
          statisticsData,
          aiModel: aiResponse.model,
          usage: aiResponse.usage,
          generatedAt: Date.now()
        }
      }
    });
  };

  async.waterfall([
    // validateParams,
    getStatisticsData,
    callAI,
    returnResult
  ], (err, data) => {
    if (_.isError(err)) {
      global.logger && global.logger.logError([err], req.originalUrl, req.body);
      global.MailUtil && global.MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  });
};
