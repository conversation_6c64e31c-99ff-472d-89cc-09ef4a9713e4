const _ = require('lodash');
const async = require('async');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');
const MeetingScheduleModel = require('../../../models/meetingSchedule');

module.exports = (req, res) => {
  const userId = _.get(req, 'user.id');
  const { week } = req.body; // week có thể là 'current', 'previous', 'next'

  const checkParams = (next) => {
    if (!userId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    // Validate week parameter
    const validWeeks = ['current', 'previous', 'next'];
    const weekType = week || 'current'; // Mặc định là tuần hiện tại

    if (!validWeeks.includes(weekType)) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Tham số week không hợp lệ. Chỉ chấp nhận: current, previous, next'
        }
      });
    }

    next(null, { weekType });
  };

  const calculateWeekRange = (result, next) => {
    const { weekType } = result;
    const now = new Date();
    
    // Tính toán ngày đầu tuần (Thứ 2)
    const currentDay = now.getDay(); // 0 = Chủ nhật, 1 = Thứ 2, ...
    const mondayOffset = currentDay === 0 ? -6 : 1 - currentDay; // Số ngày cần trừ để về Thứ 2
    
    const monday = new Date(now);
    monday.setDate(now.getDate() + mondayOffset);
    monday.setHours(0, 0, 0, 0);

    // Tính toán tuần cần lấy
    let startOfWeek = new Date(monday);
    
    if (weekType === 'previous') {
      startOfWeek.setDate(monday.getDate() - 7);
    } else if (weekType === 'next') {
      startOfWeek.setDate(monday.getDate() + 7);
    }
    
    // Ngày cuối tuần (Chủ nhật)
    const endOfWeek = new Date(startOfWeek);
    endOfWeek.setDate(startOfWeek.getDate() + 6);
    endOfWeek.setHours(23, 59, 59, 999);

    next(null, {
      startTime: startOfWeek.getTime(),
      endTime: endOfWeek.getTime(),
      weekType
    });
  };

  const getMeetingSchedules = (result, next) => {
    const { startTime, endTime, weekType } = result;
    
    const query = {
      status: 1, // Chỉ lấy cuộc họp đang hoạt động
      startTime: {
        $gte: startTime,
        $lte: endTime
      }
    };

    MeetingScheduleModel.find(query)
      .populate('officers', 'name') // Populate thông tin cán bộ
      .populate('assignedBy', 'name') // Populate thông tin người tạo lịch
      .sort({ startTime: 1 }) // Sắp xếp theo thời gian bắt đầu
      .exec((err, meetings) => {
        if (err) {
          return next(err);
        }

        // Nhóm cuộc họp theo ngày
        const groupedMeetings = {};
        meetings.forEach(meeting => {
          const meetingDate = new Date(meeting.startTime);
          const dateKey = meetingDate.toISOString().split('T')[0]; // YYYY-MM-DD (key for grouping)
          const formattedDate = `${meetingDate.getDate().toString().padStart(2, '0')}/${(meetingDate.getMonth() + 1).toString().padStart(2, '0')}`;
          
          if (!groupedMeetings[dateKey]) {
            groupedMeetings[dateKey] = {
              date: formattedDate, // DD/MM format
              dayOfWeek: meetingDate.toLocaleDateString('vi-VN', { weekday: 'long' }),
              meetings: []
            };
          }
          
          groupedMeetings[dateKey].meetings.push(meeting);
        });

        // Chuyển đổi object thành array và sắp xếp theo ngày
        const sortedDays = Object.values(groupedMeetings).sort((a, b) => 
          new Date(a.date).getTime() - new Date(b.date).getTime()
        );

        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          data: {
            weekType: weekType,
            weekRange: {
              startDate: `${new Date(startTime).getDate().toString().padStart(2, '0')}/${(new Date(startTime).getMonth() + 1).toString().padStart(2, '0')}`,
              endDate: `${new Date(endTime).getDate().toString().padStart(2, '0')}/${(new Date(endTime).getMonth() + 1).toString().padStart(2, '0')}`
            },
            totalMeetings: meetings.length,
            schedule: sortedDays
          }
        });
      });
  };

  async.waterfall([checkParams, calculateWeekRange, getMeetingSchedules], (err, data) => {
    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      });

    res.json(data || err);
  });
};
