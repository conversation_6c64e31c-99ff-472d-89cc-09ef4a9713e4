const _ = require('lodash');
const async = require('async');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');
const MeetingScheduleModel = require('../../../models/meetingSchedule');
const UserModel = require('../../../models/user');

module.exports = (req, res) => {
  const userId = _.get(req, 'user.id');
  const { _id, startTime, endTime, officers, topic, content, attachments } = req.body;

  const checkParams = (next) => {
    if (!userId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    if (!_id) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Vui lòng cung cấp ID lịch họp'
        }
      });
    }

    // Validate ObjectId
    if (typeof _id !== 'string' || !_id.match(/^[0-9a-fA-F]{24}$/)) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'ID lịch họp không hợp lệ'
        }
      });
    }

    // Validate startTime nếu có
    if (startTime !== undefined && (typeof startTime !== 'number' || startTime <= 0)) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Thời gian bắt đầu không hợp lệ'
        }
      });
    }

    // Validate endTime nếu có
    if (endTime !== undefined && endTime !== null && (typeof endTime !== 'number' || endTime <= 0)) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Thời gian kết thúc không hợp lệ'
        }
      });
    }

    // Kiểm tra thời gian bắt đầu phải nhỏ hơn thời gian kết thúc (nếu cả hai đều có)
    if (startTime && endTime && startTime >= endTime) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Thời gian bắt đầu phải nhỏ hơn thời gian kết thúc'
        }
      });
    }

    // Validate officers nếu có
    if (officers !== undefined) {
      if (!Array.isArray(officers) || officers.length === 0) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Thông báo',
            body: 'Danh sách cán bộ phải là array và không được rỗng'
          }
        });
      }

      // Validate ObjectId của officers
      const isValidObjectId = (id) => {
        return typeof id === 'string' && id.match(/^[0-9a-fA-F]{24}$/);
      };

      if (!officers.every(officerId => isValidObjectId(officerId))) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Thông báo',
            body: 'ID cán bộ không hợp lệ'
          }
        });
      }
    }

    // Validate topic nếu có
    if (topic !== undefined && (typeof topic !== 'string' || topic.trim().length === 0)) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Chủ đề cuộc họp không hợp lệ'
        }
      });
    }

    // Validate attachments nếu có
    if (attachments !== undefined && (!Array.isArray(attachments) || !attachments.every(att => typeof att === 'string'))) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Dữ liệu tệp đính kèm không hợp lệ'
        }
      });
    }

    next();
  };

  const validateOfficers = (next) => {
    // Nếu không cập nhật officers thì bỏ qua bước này
    if (!officers) {
      return next();
    }

    // Kiểm tra tất cả officer IDs có tồn tại trong database không
    UserModel.find({
      _id: { $in: officers },
      status: 1 // Chỉ lấy user đang active
    }, (err, users) => {
      if (err) {
        return next(err);
      }

      const foundUserIds = users.map(user => user._id.toString());
      const missingOfficers = officers.filter(officerId => !foundUserIds.includes(officerId));

      if (missingOfficers.length > 0) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Thông báo',
            body: `Không tìm thấy hoặc cán bộ không hoạt động với ID: ${missingOfficers.join(', ')}`
          }
        });
      }

      next();
    });
  };

  const updateMeetingSchedule = (next) => {
    const updateData = {
      updatedAt: Date.now()
    };

    // Cập nhật các trường nếu có
    if (startTime !== undefined) updateData.startTime = startTime;
    if (endTime !== undefined) {
      if (endTime === null) {
        updateData.$unset = { endTime: 1 };
      } else {
        updateData.endTime = endTime;
      }
    }
    if (officers !== undefined) updateData.officers = officers;
    if (topic !== undefined) updateData.topic = topic.trim();
    if (content !== undefined) {
      if (content === null || content.trim() === '') {
        if (!updateData.$unset) updateData.$unset = {};
        updateData.$unset.content = 1;
      } else {
        updateData.content = content.trim();
      }
    }
    if (attachments !== undefined) {
      if (attachments.length === 0) {
        if (!updateData.$unset) updateData.$unset = {};
        updateData.$unset.attachments = 1;
      } else {
        updateData.attachments = attachments;
      }
    }

    // Kiểm tra có dữ liệu để cập nhật không (ngoài updatedAt)
    const hasUpdateData = Object.keys(updateData).some(key => key !== 'updatedAt');
    const hasUnsetData = updateData.$unset && Object.keys(updateData.$unset).length > 0;
    
    if (!hasUpdateData && !hasUnsetData) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Không có dữ liệu để cập nhật'
        }
      });
    }

    const updateQuery = { $set: _.omit(updateData, '$unset') };
    if (updateData.$unset) {
      updateQuery.$unset = updateData.$unset;
    }

    MeetingScheduleModel.findOneAndUpdate(
      { _id: _id, status: 1 }, // Chỉ cập nhật lịch họp đang hoạt động
      updateQuery,
      { new: true }
    )
    .populate('officers', 'name email phone')
    .populate('assignedBy', 'name email')
    .exec((err, meeting) => {
      if (err) {
        return next(err);
      }

      if (!meeting) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Thông báo',
            body: 'Lịch họp không tồn tại hoặc đã bị xóa'
          }
        });
      }

      next(null, {
        code: CONSTANTS.CODE.SUCCESS,
        data: meeting
      });
    });
  };

  async.waterfall([checkParams, validateOfficers, updateMeetingSchedule], (err, data) => {
    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      });

    res.json(data || err);
  });
};
